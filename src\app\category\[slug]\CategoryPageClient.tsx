'use client';

import React, { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { categoryService, linkService } from '@/firebase/firestore';
import { Category, LinkWithDetails, FilterOptions } from '@/types';
import LinkCard from '@/components/UI/LinkCard';
import {
  ArrowLeftIcon,
  FunnelIcon,
  PlusIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

interface CategoryPageClientProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function CategoryPageClient({ params }: CategoryPageClientProps) {
  const resolvedParams = React.use(params);
  const { user } = useAuth();
  const [category, setCategory] = useState<Category | null>(null);
  const [links, setLinks] = useState<LinkWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [linksLoading, setLinksLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    sortBy: 'newest',
    timeRange: 'all'
  });

  // Load category and initial links
  useEffect(() => {
    const loadCategory = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const categoryData = await categoryService.getBySlug(resolvedParams.slug);
        if (!categoryData) {
          notFound();
          return;
        }
        
        setCategory(categoryData);
        await loadLinks(categoryData.id);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Fehler beim Laden der Kategorie');
      } finally {
        setLoading(false);
      }
    };

    loadCategory();
  }, [resolvedParams.slug]);

  // Load links for the category
  const loadLinks = async (categoryId: string) => {
    try {
      setLinksLoading(true);
      
      // TODO: Implement filtering and search in linkService
      const response = await linkService.getByCategory(categoryId, 20);
      setLinks(response.data);
      
    } catch (err) {
      console.error('Fehler beim Laden der Links:', err);
      setLinks([]);
    } finally {
      setLinksLoading(false);
    }
  };

  // Filter links based on search query
  const filteredLinks = links.filter(link => {
    if (!searchQuery.trim()) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      link.title.toLowerCase().includes(query) ||
      link.description?.toLowerCase().includes(query) ||
      link.tags?.some(tag => tag.toLowerCase().includes(query))
    );
  });

  // Sort links based on current filter
  const sortedLinks = [...filteredLinks].sort((a, b) => {
    switch (filters.sortBy) {
      case 'newest':
        return new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime();
      case 'oldest':
        return new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime();
      case 'rating':
        return (b.averageRating || 0) - (a.averageRating || 0);
      case 'popular':
        return (b.totalComments || 0) - (a.totalComments || 0);
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-48 mb-6"></div>
            <div className="h-12 bg-gray-200 rounded w-full mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3 mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || 'Kategorie nicht gefunden'}
            </h1>
            <Link
              href="/categories"
              className="text-blue-600 hover:text-blue-700"
            >
              Zurück zu den Kategorien
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/categories"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Alle Kategorien
          </Link>
          
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{category.name}</h1>
              {category.description && (
                <p className="mt-2 text-gray-600">{category.description}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                {category.totalLinks || 0} {(category.totalLinks || 0) === 1 ? 'Link' : 'Links'}
              </p>
            </div>
            
            {user && (
              <div className="mt-4 md:mt-0">
                <Link
                  href="/submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Link hinzufügen
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Links durchsuchen..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            {/* Sort Filter */}
            <div className="flex items-center space-x-2">
              <FunnelIcon className="h-5 w-5 text-gray-400" />
              <select
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
              >
                <option value="newest">Neueste zuerst</option>
                <option value="oldest">Älteste zuerst</option>
                <option value="rating">Beste Bewertung</option>
                <option value="popular">Beliebteste</option>
              </select>
            </div>
          </div>
        </div>

        {/* Links List */}
        <div className="space-y-4">
          {linksLoading ? (
            [...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : sortedLinks.length > 0 ? (
            sortedLinks.map((link) => (
              <LinkCard
                key={link.id}
                link={link}
                showCategory={false}
              />
            ))
          ) : (
            <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
              <div className="text-gray-400 mb-4">
                <FunnelIcon className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'Keine Links gefunden' : 'Noch keine Links vorhanden'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchQuery 
                  ? `Keine Links gefunden, die "${searchQuery}" enthalten.`
                  : 'Sei der Erste und teile einen Link in dieser Kategorie!'
                }
              </p>
              {user && !searchQuery && (
                <Link
                  href="/submit"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Ersten Link hinzufügen
                </Link>
              )}
            </div>
          )}
        </div>

        {/* Load More (Future Feature) */}
        {sortedLinks.length >= 20 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 rounded-md text-base font-medium text-gray-700 bg-white hover:bg-gray-50">
              Mehr laden
            </button>
          </div>
        )}

      </div>
    </div>
  );
}
