// Generate static params for build (empty for dynamic content)
export async function generateStaticParams() {
  return [];
}

import LinkDetailClient from './LinkDetailClient';

export default function LinkDetailPage() {
  return <LinkDetailClient />;
}

  const handleRating = async (rating: number) => {
    if (!user || !linkId || submittingRating) return;

    try {
      setSubmittingRating(true);
      await ratingService.create(linkId, user.id, rating);
      
      // Reload user rating
      const newRating = await ratingService.getUserRating(linkId, user.id);
      setUserRating(newRating);
      
      // Update link data
      await loadLinkDetails();
    } catch (error) {
      console.error('Error submitting rating:', error);
    } finally {
      setSubmittingRating(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!user || !linkId || !newComment.trim() || submittingComment) return;

    try {
      setSubmittingComment(true);
      console.log('Submitting comment:', { linkId, userId: user.id, content: newComment.trim() });
      
      const commentId = await commentService.create(linkId, user.id, newComment.trim());
      console.log('Comment created with ID:', commentId);
      
      setNewComment('');
      
      // Reload comments
      console.log('Reloading comments...');
      const commentsData = await commentService.getByLinkNested(linkId);
      console.log('Loaded comments:', commentsData);
      setComments(commentsData as CommentWithReplies[]);
    } catch (error) {
      console.error('Error submitting comment:', error);
      alert('Fehler beim Senden des Kommentars. Bitte versuche es erneut.');
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleSubmitReply = async (parentCommentId: string) => {
    if (!user || !linkId || !replyText.trim() || submittingComment) return;

    try {
      setSubmittingComment(true);
      // Use the parentId parameter for proper nested comments
      await commentService.create(linkId, user.id, replyText.trim(), parentCommentId);
      setReplyText('');
      setReplyingTo(null);
      
      // Reload comments
      const commentsData = await commentService.getByLinkNested(linkId);
      setComments(commentsData as CommentWithReplies[]);
    } catch (error) {
      console.error('Error submitting reply:', error);
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleReportLink = () => {
    if (!link) return;
    setReportModal({
      isOpen: true,
      targetType: 'link',
      targetId: link.id,
      targetTitle: link.title
    });
  };

  const handleReportComment = (comment: CommentWithReplies) => {
    setReportModal({
      isOpen: true,
      targetType: 'comment',
      targetId: comment.id,
      targetTitle: comment.content.substring(0, 50) + (comment.content.length > 50 ? '...' : '')
    });
  };

  const closeReportModal = () => {
    setReportModal({
      isOpen: false,
      targetType: 'link',
      targetId: '',
      targetTitle: ''
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!link) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Link nicht gefunden</h1>
          <p className="text-gray-600">Der angeforderte Link existiert nicht oder wurde entfernt.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Link Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{link.title}</h1>
              <p className="text-gray-600 mb-4">{link.description}</p>
              
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                <span>Domain: {link.domain}</span>
                <span>•</span>
                <span>Eingereicht von: {link.submitter?.displayName || 'Unbekannt'}</span>
                <span>•</span>
                <span>{new Date(link.submittedAt).toLocaleDateString('de-DE')}</span>
              </div>

              {/* Tags */}
              {link.tags && link.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {link.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>

            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors mr-2"
            >
              <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
              Link öffnen
            </a>

            {user && (
              <button
                onClick={handleReportLink}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors mr-2"
              >
                <FlagIcon className="h-4 w-4 mr-2" />
                Melden
              </button>
            )}

            {/* Debug Buttons - Only for admins */}
            {isAdmin(user) && (
              <>
                <button
                  onClick={async () => {
                    console.log('🧪 Testing Firebase connectivity...');
                    try {
                      const testDoc = await linkService.getById(linkId!);
                      console.log('🧪 Link fetch test successful:', testDoc);

                      const testComments = await commentService.getByLink(linkId!);
                      console.log('🧪 Comments fetch test successful:', testComments);

                      alert(`Debug: Link OK, ${testComments.length} comments found`);
                    } catch (error) {
                      console.error('🧪 Firebase test failed:', error);
                      alert(`Debug: Firebase error - ${error}`);
                    }
                  }}
                  className="inline-flex items-center px-3 py-2 border border-orange-300 text-sm font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100 transition-colors mr-2"
                >
                  🧪 Debug
                </button>

                <button
                  onClick={async () => {
                    console.log('👤 Current user object:', user);
                    try {
                      const userFromDB = await userService.getById(user.id);
                      console.log('👤 User from database:', userFromDB);
                      alert(`User Debug:\nID: ${user.id}\nName: ${user.displayName}\nDB: ${userFromDB ? 'Found' : 'Not Found'}`);
                    } catch (error) {
                      console.error('👤 User fetch failed:', error);
                      alert(`User Error: ${error}`);
                    }
                  }}
                  className="inline-flex items-center px-3 py-2 border border-purple-300 text-sm font-medium rounded-md text-purple-700 bg-purple-50 hover:bg-purple-100 transition-colors"
                >
                  👤 User Debug
                </button>
              </>
            )}
          </div>

          {/* Rating Section */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Bewertung</h3>
                <div className="flex items-center mt-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => handleRating(star)}
                      disabled={!user || submittingRating}
                      className={`h-5 w-5 ${
                        userRating && star <= userRating.rating
                          ? 'text-yellow-400'
                          : 'text-gray-300 hover:text-yellow-400'
                      } ${!user ? 'cursor-not-allowed' : 'cursor-pointer'} transition-colors`}
                    >
                      {userRating && star <= userRating.rating ? (
                        <StarIcon />
                      ) : (
                        <StarOutlineIcon />
                      )}
                    </button>
                  ))}
                  <span className="ml-2 text-sm text-gray-600">
                    ({link.averageRating?.toFixed(1) || '0.0'} • {link.totalRatings || 0} Bewertungen)
                  </span>
                </div>
              </div>
              
              {!user && (
                <p className="text-sm text-gray-500">Melde dich an, um zu bewerten</p>
              )}
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <ChatBubbleLeftIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">
              Kommentare ({comments.length})
            </h2>
          </div>

          {/* New Comment Form */}
          {user ? (
            <div className="mb-6">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Schreibe einen Kommentar..."
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="mt-2 flex justify-end">
                <button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim() || submittingComment}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors mr-2"
                >
                  {submittingComment ? 'Wird gesendet...' : 'Kommentieren'}
                </button>

                {/* Test Comment Button - Only for admins */}
                {isAdmin(user) && (
                  <button
                    onClick={async () => {
                      try {
                        console.log('🧪 Creating test comment...');
                        const commentId = await commentService.create(linkId!, user.id, `Test Kommentar ${Date.now()}`);
                        console.log('🧪 Test comment created:', commentId);

                        // Reload comments
                        const commentsData = await commentService.getByLinkNested(linkId!);
                        setComments(commentsData as CommentWithReplies[]);
                        alert('Test-Kommentar erstellt!');
                      } catch (error) {
                        console.error('🧪 Test comment failed:', error);
                        alert(`Fehler: ${error}`);
                      }
                    }}
                    className="px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 rounded-md transition-colors"
                  >
                    🧪 Test Kommentar
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
              <p className="text-gray-600 text-center">
                <span className="font-medium">Melde dich an, um einen Kommentar zu schreiben.</span>
              </p>
              <div className="mt-3 flex justify-center space-x-4">
                <Link
                  href="/login"
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Anmelden
                </Link>
                <span className="text-gray-400">•</span>
                <Link
                  href="/register"
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Registrieren
                </Link>
              </div>
            </div>
          )}

          {/* Comments List */}
          <div className="space-y-4">
            {comments.length === 0 ? (
              <p className="text-gray-500 text-center py-8">Noch keine Kommentare vorhanden.</p>
            ) : (
              comments.map((comment) => (
                <div key={comment.id} className="border-b border-gray-100 last:border-b-0 pb-4 last:pb-0">
                  <CommentItem
                    comment={comment}
                    onReply={(commentId) => setReplyingTo(commentId === replyingTo ? null : commentId)}
                    onReport={handleReportComment}
                    replyingTo={replyingTo}
                    replyText={replyText}
                    onReplyTextChange={(text) => setReplyText(text)}
                    onSubmitReply={handleSubmitReply}
                    onCancelReply={() => setReplyingTo(null)}
                    submittingComment={submittingComment}
                    user={user}
                  />
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Report Modal */}
      <ReportModal
        isOpen={reportModal.isOpen}
        onClose={closeReportModal}
        targetType={reportModal.targetType}
        targetId={reportModal.targetId}
        targetTitle={reportModal.targetTitle}
      />
    </div>
  );
} 